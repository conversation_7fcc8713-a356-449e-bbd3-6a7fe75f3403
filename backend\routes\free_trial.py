"""
Free trial routes for non-authenticated users.
Provides limited access to book and chat functionality.
"""

from fastapi import APIRouter, Depends, HTTPException, Request
from typing import Dict, List, Optional
from pydantic import BaseModel, Field
from datetime import datetime
import logging
import uuid

from backend.utils.free_trial import (
    free_trial_manager, 
    check_free_trial_limit, 
    get_free_trial_user
)
from backend.ai.chat_service import ChatService
from backend.ai import character
from backend.ai.prompt_utils import format_book_context
from backend.config import config
from backend.utils.security import get_anti_bot, get_request_validator
import re
import random

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/free-trial", tags=["free-trial"])


def is_content_safe(text: str, allow_empty: bool = False) -> bool:
    """
    Basic content safety filter for free trial users.
    Blocks potentially harmful, inappropriate, or spam content.
    """
    if not text or len(text.strip()) == 0:
        return allow_empty
    
    # Check message length (prevent very long messages)
    if len(text) > 2000:
        return False
    
    # Convert to lowercase for checking
    text_lower = text.lower()
    
    # Block potentially harmful content
    harmful_patterns = [
        # Personal information requests
        r'\b(ssn|social security|credit card|password|bank account)\b',
        # Inappropriate content
        r'\b(porn|sex|nude|naked|explicit)\b',
        # Spam indicators
        r'\b(click here|buy now|free money|get rich|weight loss)\b',
        # Harmful instructions
        r'\b(hack|virus|malware|exploit|ddos)\b',
        # Excessive repeated characters (spam)
        r'(.)\1{10,}',
        # URLs (prevent link sharing)
        r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+',
    ]
    
    for pattern in harmful_patterns:
        if re.search(pattern, text_lower):
            logger.warning(f"Blocked potentially harmful content: {pattern}")
            return False
    
    # Check for excessive special characters (potential spam)
    special_char_ratio = sum(1 for char in text if not char.isalnum() and not char.isspace()) / len(text)
    if special_char_ratio > 0.3:
        logger.warning(f"Blocked content with excessive special characters: {special_char_ratio:.2f} ratio")
        return False
    
    return True


def sanitize_book_data(book_data: dict) -> dict:
    """
    Sanitize book data from free trial users.
    """
    sanitized = {}
    
    # Preserve id if present
    if 'id' in book_data:
        sanitized['id'] = book_data['id']
    
    # Sanitize title
    title = book_data.get('title', '').strip()
    if len(title) > 200:
        title = title[:200]
    sanitized['title'] = re.sub(r'[<>"\']', '', title)
    
    # Sanitize author
    author = book_data.get('author', '').strip()
    if len(author) > 100:
        author = author[:100]
    sanitized['author'] = re.sub(r'[<>"\']', '', author)
    
    # Sanitize other fields
    sanitized['isbn'] = book_data.get('isbn', '')[:50] if book_data.get('isbn') else None
    sanitized['genre'] = book_data.get('genre', '')[:50] if book_data.get('genre') else None
    
    # Validate publication year
    pub_year = book_data.get('publication_year')
    if pub_year and isinstance(pub_year, int) and 1000 <= pub_year <= 2030:
        sanitized['publication_year'] = pub_year
    else:
        sanitized['publication_year'] = None
    
    return sanitized


class FreeTrialBook(BaseModel):
    """Simplified book model for free trial users."""
    id: Optional[str] = None
    title: str
    author: str
    isbn: Optional[str] = None
    genre: Optional[str] = None
    publication_year: Optional[int] = None
    added_at: datetime = Field(default_factory=datetime.utcnow)


class FreeTrialChatRequest(BaseModel):
    """Chat request for free trial users."""
    message: str
    book_id: str
    character_id: str
    book_context: Optional[Dict] = None  # Book details for context


class FreeTrialResponse(BaseModel):
    """Standard response for free trial endpoints."""
    success: bool
    data: Optional[Dict] = None
    usage_info: Dict
    message: Optional[str] = None


class ChallengeRequest(BaseModel):
    """Request to verify an anti-bot challenge."""
    challenge_id: str
    answer: str


# Popular book suggestions for free trial users
FREE_TRIAL_SUGGESTIONS = [
    {
        "id": "ft-1",
        "title": "The Midnight Library",
        "author": "Matt Haig",
        "description": "A novel about a library between life and death where each book represents a different path your life could have taken."
    },
    {
        "id": "ft-2", 
        "title": "Dune",
        "author": "Frank Herbert",
        "description": "Set on the desert planet Arrakis, Dune tells the story of Paul Atreides, whose family accepts stewardship of the planet that produces the 'spice'."
    },
    {
        "id": "ft-3",
        "title": "The Seven Husbands of Evelyn Hugo",
        "author": "Taylor Jenkins Reid", 
        "description": "A reclusive Hollywood icon finally decides to give her life story to an unknown journalist, revealing stunning secrets."
    },
    {
        "id": "ft-4",
        "title": "Where the Crawdads Sing",
        "author": "Delia Owens",
        "description": "A novel about a young woman who grows up isolated in the marshes of North Carolina and later becomes the prime suspect in a murder case."
    },
    {
        "id": "ft-5",
        "title": "Educated",
        "author": "Tara Westover",
        "description": "A memoir about a woman who grows up in a survivalist family in Idaho and eventually earns a PhD from Cambridge University."
    },
    {
        "id": "ft-6",
        "title": "The Alchemist",
        "author": "Paulo Coelho",
        "description": "A philosophical novel about a young Andalusian shepherd named Santiago and his journey to find a hidden treasure at the Egyptian pyramids."
    },
    {
        "id": "ft-7",
        "title": "Atomic Habits",
        "author": "James Clear",
        "description": "A comprehensive guide to building good habits and breaking bad ones, based on the latest research in behavioral psychology."
    },
    {
        "id": "ft-8",
        "title": "The Silent Patient",
        "author": "Alex Michaelides",
        "description": "A psychological thriller about Alicia Berenson, a famous painter who has been silent since being accused of murdering her husband."
    },
    {
        "id": "ft-9",
        "title": "Becoming",
        "author": "Michelle Obama",
        "description": "The memoir of former First Lady Michelle Obama, chronicling her experiences from childhood through her years in the White House."
    },
    {
        "id": "ft-10",
        "title": "The Night Circus",
        "author": "Erin Morgenstern",
        "description": "A fantasy novel about a mysterious circus that arrives without warning and is only open at night."
    },
    {
        "id": "ft-11",
        "title": "1984",
        "author": "George Orwell",
        "description": "A dystopian social science fiction novel about a totalitarian society ruled by Big Brother."
    },
    {
        "id": "ft-12",
        "title": "The Power of Habit",
        "author": "Charles Duhigg",
        "description": "Explores the science behind habit creation and reformation, explaining how habits work and how they can be changed."
    }
]


@router.get("/usage")
async def get_usage_info(request: Request) -> FreeTrialResponse:
    """Get current usage information for the free trial user."""
    usage_info = await free_trial_manager.get_usage_info(request)
    
    return FreeTrialResponse(
        success=True,
        data=usage_info,
        usage_info=usage_info
    )


@router.get("/suggestions")
async def get_free_trial_suggestions(
    request: Request,
    free_user: Dict = Depends(get_free_trial_user)
) -> FreeTrialResponse:
    """Get book suggestions for free trial users."""
    usage_info = await free_trial_manager.get_usage_info(request)
    
    # Always return suggestions - randomize for variety
    suggestions = FREE_TRIAL_SUGGESTIONS.copy()
    random.shuffle(suggestions)
    
    return FreeTrialResponse(
        success=True,
        data={"suggestions": suggestions[:5]},  # Return 5 random suggestions
        usage_info=usage_info,
        message="Popular book recommendations for you!"
    )


@router.get("/suggestions/single")
async def get_single_free_trial_suggestion(
    request: Request,
    free_user: Dict = Depends(get_free_trial_user)
) -> FreeTrialResponse:
    """Get a single book suggestion to replace one that was added."""
    usage_info = await free_trial_manager.get_usage_info(request)
    
    # Return a random suggestion
    suggestion = random.choice(FREE_TRIAL_SUGGESTIONS)
    
    return FreeTrialResponse(
        success=True,
        data={"suggestion": suggestion},
        usage_info=usage_info
    )


@router.post("/challenge")
async def get_challenge(
    request: Request,
    free_user: Dict = Depends(get_free_trial_user)
) -> Dict:
    """
    Get an anti-bot challenge for suspicious activity.
    """
    anti_bot = get_anti_bot()
    challenge = anti_bot.generate_challenge(free_user["client_id"])
    
    return {
        "success": True,
        "challenge": challenge,
        "message": "Please complete this challenge to continue."
    }


@router.post("/verify-challenge")
async def verify_challenge(
    challenge_request: ChallengeRequest,
    request: Request,
    free_user: Dict = Depends(get_free_trial_user)
) -> Dict:
    """
    Verify an anti-bot challenge response.
    """
    anti_bot = get_anti_bot()
    is_valid = anti_bot.verify_challenge(
        challenge_request.challenge_id,
        challenge_request.answer,
        free_user["client_id"]
    )
    
    if is_valid:
        return {
            "success": True,
            "message": "Challenge completed successfully!"
        }
    else:
        raise HTTPException(
            status_code=400,
            detail="Challenge verification failed. Please try again."
        )


@router.post("/books/add")
async def add_book(
    request: Request,
    free_user: Dict = Depends(get_free_trial_user)
) -> FreeTrialResponse:
    """Add a book to the free trial library (max 1 book) with AI suggestions."""
    try:
        data = await request.json()
        
        # Validate required fields
        if not data or 'title' not in data:
            raise HTTPException(
                status_code=400,
                detail="Title is required"
            )
        
        title = data.get('title', '').strip()
        author = data.get('author', '').strip() if data.get('author') else None
        isbn = data.get('isbn', '').strip() if data.get('isbn') else None
        genre = data.get('genre', '').strip() if data.get('genre') else None
        publication_year = data.get('publication_year')
        use_ai_suggestions = data.get('use_ai_suggestions', False)
        
        # Validate book content - title is required, author can be empty
        if not is_content_safe(title):
            raise HTTPException(
                status_code=400,
                detail="Book title is required and cannot contain inappropriate content."
            )
        
        if author and not is_content_safe(author, allow_empty=True):
            raise HTTPException(
                status_code=400,
                detail="Author field contains inappropriate content."
            )
        
        # Check if user can add a book
        if not await free_trial_manager.can_add_book(request):
            raise HTTPException(
                status_code=400,
                detail="Free trial users can only add one book. Please create an account to add more books."
            )
        
        ai_changes = None
        
        # AI suggestions for free trial users
        if use_ai_suggestions:
            try:
                from backend.ai.book_services import BookServices
                ai_service = BookServices()
                
                # Validate title
                valid, corrected_title = ai_service.validate_book_title(title)
                # Generate author suggestion if missing or provided
                generated_author = ai_service.generate_author_name(title)
                
                # Determine if AI suggests changes
                ai_changes = {}
                if corrected_title and corrected_title != title and corrected_title != "No match found":
                    ai_changes['corrected_title'] = corrected_title
                    ai_changes['original_title'] = title
                if generated_author and generated_author != (author or '') and generated_author != "No match found":
                    ai_changes['generated_author'] = generated_author
                    ai_changes['original_author'] = author
                
                # If AI suggests changes, return them without adding the book yet
                if ai_changes:
                    usage_info = await free_trial_manager.get_usage_info(request)
                    return FreeTrialResponse(
                        success=True,
                        data={
                            "ai_changes": ai_changes,
                            "message": "AI suggestions available. Please confirm changes."
                        },
                        usage_info=usage_info
                    )
            except Exception as e:
                logger.warning(f"AI suggestions failed for free trial user: {e}")
                # Continue without AI suggestions if they fail
        
        # Generate ID for the book
        book_id = str(uuid.uuid4())
        
        # Create book data
        book_data = {
            'id': book_id,
            'title': title,
            'author': author,
            'isbn': isbn,
            'genre': genre,
            'publication_year': publication_year,
            'added_at': datetime.utcnow()
        }
        
        # Sanitize book data
        book_data = sanitize_book_data(book_data)
        
        # Create sanitized book object
        sanitized_book = FreeTrialBook(**book_data)
        
        # Add the book
        success = await free_trial_manager.add_book(request, sanitized_book.id)
        if not success:
            raise HTTPException(
                status_code=400,
                detail="Failed to add book. You may have already added this book."
            )
        
        usage_info = await free_trial_manager.get_usage_info(request)
        
        return FreeTrialResponse(
            success=True,
            data={"book": sanitized_book.dict()},
            usage_info=usage_info,
            message="Book added successfully to your free trial library."
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error adding free trial book: {e}")
        raise HTTPException(
            status_code=500,
            detail="Error adding book. Please try again."
        )


@router.get("/books")
async def get_books(
    request: Request,
    free_user: Dict = Depends(get_free_trial_user)
) -> FreeTrialResponse:
    """Get the list of books for the free trial user."""
    book_ids = await free_trial_manager.get_user_books(request)
    usage_info = await free_trial_manager.get_usage_info(request)
    
    # In a real implementation, you would fetch book details from a database
    # For now, we'll return the IDs with a note
    return FreeTrialResponse(
        success=True,
        data={"book_ids": book_ids},
        usage_info=usage_info
    )


@router.delete("/books/{book_id}")
async def delete_book(
    book_id: str,
    request: Request,
    free_user: Dict = Depends(get_free_trial_user)
) -> FreeTrialResponse:
    """Delete a book from the free trial library."""
    # Verify the user has this book
    user_books = await free_trial_manager.get_user_books(request)
    if book_id not in user_books:
        raise HTTPException(
            status_code=404,
            detail="Book not found in your library."
        )
    
    # Remove the book from the user's list
    success = await free_trial_manager.remove_book(request, book_id)
    if not success:
        raise HTTPException(
            status_code=500,
            detail="Failed to delete book."
        )
    
    usage_info = await free_trial_manager.get_usage_info(request)
    
    return FreeTrialResponse(
        success=True,
        data={"book_id": book_id},
        usage_info=usage_info,
        message="Book deleted successfully from your free trial library."
    )


@router.post("/chat/message")
async def send_chat_message(
    chat_request: FreeTrialChatRequest,
    request: Request,
    free_user: Dict = Depends(get_free_trial_user)
) -> FreeTrialResponse:
    """
    Send a chat message as a free trial user.
    Limited to 5 messages per day.
    """
    try:
        # Check for abuse before processing
        if await free_trial_manager.check_abuse(request):
            raise HTTPException(
                status_code=429,
                detail="Too many requests. Please create an account to continue using the service."
            )
        
        # Check usage limits BEFORE updating usage
        usage_info = await free_trial_manager.get_usage_info(request)
        if usage_info["messages_remaining"] <= 0:
            raise HTTPException(
                status_code=429,
                detail={
                    "error": "Daily limit reached",
                    "message": "You've reached your daily limit of free messages. Please create an account for unlimited access.",
                    "reset_time": usage_info["reset_time"],
                    "usage_info": usage_info
                }
            )
        
        # Security validation
        request_validator = get_request_validator()
        request_headers = dict(request.headers)
        
        # Check for suspicious request patterns
        if request_validator.is_suspicious_request(chat_request.dict(), request_headers):
            logger.warning(f"Suspicious request detected from {free_user['client_id']}")
            # Could trigger CAPTCHA challenge here in a real implementation
            
        # Content safety checks
        if not is_content_safe(chat_request.message):
            raise HTTPException(
                status_code=400,
                detail="Message contains inappropriate content. Please keep conversations friendly and appropriate."
            )
        
        if not request_validator.validate_message_content(chat_request.message):
            raise HTTPException(
                status_code=400,
                detail="Message content appears to be spam or repetitive. Please write a meaningful message."
            )
        
        # Book access validation removed for testing - users can chat about any book
        
        # Update usage (this actually consumes a message)
        can_send, remaining, reset_time = await free_trial_manager.check_and_update_usage(request)
        
        # This should not fail since we already checked above, but safety check
        if not can_send:
            raise HTTPException(
                status_code=429,
                detail={
                    "error": "Daily limit reached",
                    "message": "You've used all your free messages for today. Create an account for unlimited access!",
                    "reset_time": reset_time
                }
            )
        
        # Free trial users get limited conversation history through ChatService
        
        # Initialize chat service (using existing pattern)
        chat_service = ChatService()
        
        # Get character using existing character module
        char = character.get_character(chat_request.character_id)
        if not char:
            char = character.CHAR_AVA  # Default fallback
        
        # Prepare book context using existing format_book_context function
        book_context = ""
        book_title = None
        book_author = None
        
        if chat_request.book_context:
            book_context_data = {
                'title': chat_request.book_context.get('title', 'Unknown'),
                'author': chat_request.book_context.get('author', 'Unknown'),
            }
            book_title = book_context_data['title']
            book_author = book_context_data['author']
            
            # Use existing format_book_context function for consistency
            book_context = format_book_context([book_context_data])
        
        # Use existing chat service with free trial conversation ID
        response_content = chat_service.chat(
            message=chat_request.message,
            character=chat_request.character_id,
            conversation_id=f"free_trial_{free_user['client_id']}",
            book_id=None,  # No database book for free trial
            book_context=book_context,
            chat_id=None,
            stream=False,
            book_title=book_title,
            book_author=book_author
        )
        
        # Update usage info with new remaining count
        updated_usage = await free_trial_manager.get_usage_info(request)
        
        # Add upgrade prompt if low on messages
        upgrade_prompt = None
        if updated_usage["messages_remaining"] <= 2:
            upgrade_prompt = (
                f"You have {updated_usage['messages_remaining']} free messages remaining today. "
                "Create an account for unlimited conversations about your books!"
            )
        
        # Get character info using existing pattern
        character_info = char.get_info() if char else None
        
        return FreeTrialResponse(
            success=True,
            data={
                "text": response_content,  # Match existing field name
                "characterInfo": character_info,  # Add missing field for consistency
                "character_id": chat_request.character_id,
                "book_id": chat_request.book_id,
                "upgrade_prompt": upgrade_prompt
            },
            usage_info=updated_usage
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in free trial chat: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="An error occurred while processing your message. Please try again."
        )


@router.post("/chat/history")
async def get_chat_history(
    request: Request,
    book_id: str,
    character_id: str,
    free_user: Dict = Depends(get_free_trial_user)
) -> FreeTrialResponse:
    """
    Get chat history for a free trial user.
    Note: This would typically be stored in browser localStorage.
    """
    usage_info = await free_trial_manager.get_usage_info(request)
    
    # In a real implementation, this would retrieve from a temporary storage
    # For now, return empty history with usage info
    return FreeTrialResponse(
        success=True,
        data={
            "messages": [],
            "book_id": book_id,
            "character_id": character_id
        },
        usage_info=usage_info
    )


@router.post("/migrate-to-account")
async def migrate_to_account(
    request: Request,
    user_id: str,  # The newly created user's ID
    free_user: Dict = Depends(get_free_trial_user)
) -> FreeTrialResponse:
    """
    Migrate free trial data to a newly created account.
    This would be called after a user signs up.
    """
    # Get the user's books
    book_ids = await free_trial_manager.get_user_books(request)
    
    # In a real implementation, you would:
    # 1. Transfer the books to the user's account in the database
    # 2. Transfer any chat history from temporary storage
    # 3. Clear the free trial data
    
    usage_info = await free_trial_manager.get_usage_info(request)
    
    return FreeTrialResponse(
        success=True,
        data={
            "migrated_books": book_ids,
            "message": "Your free trial data has been transferred to your new account!"
        },
        usage_info=usage_info
    )