/**
 * Service for managing free trial functionality.
 * Handles non-authenticated user access with usage limits.
 */

import axios from './axiosConfig';

const FREE_TRIAL_KEY = 'bookworm_free_trial';
const FREE_TRIAL_BOOKS_KEY = 'bookworm_free_trial_books';
const FREE_TRIAL_CHAT_KEY = 'bookworm_free_trial_chats';

class FreeTrialService {
  constructor() {
    this.initializeStorage();
  }

  /**
   * Initialize local storage for free trial data
   */
  initializeStorage() {
    if (!localStorage.getItem(FREE_TRIAL_KEY)) {
      const initialData = {
        firstAccess: new Date().toISOString(),
        lastAccess: new Date().toISOString(),
        hasSeenWelcome: false
      };
      localStorage.setItem(FREE_TRIAL_KEY, JSON.stringify(initialData));
    }
    
    if (!localStorage.getItem(FREE_TRIAL_BOOKS_KEY)) {
      localStorage.setItem(FREE_TRIAL_BOOKS_KEY, JSON.stringify([]));
    }
    
    if (!localStorage.getItem(FREE_TRIAL_CHAT_KEY)) {
      localStorage.setItem(FREE_TRIAL_CHAT_KEY, JSON.stringify({}));
    }
  }

  /**
   * Get current usage information from the backend
   */
  async getUsageInfo() {
    try {
      const response = await axios.get('/api/free-trial/usage');
      return response.data;
    } catch (error) {
      console.error('Error fetching usage info:', error);
      throw error;
    }
  }

  /**
   * Add a book to the free trial library
   */
  async addBook(bookData) {
    try {
      const response = await axios.post('/api/free-trial/books/add', bookData);
      
      // Check if we got AI suggestions
      if (response.data.data?.ai_changes) {
        // Return the AI suggestions for the UI to handle
        return response.data.data;
      }
      
      // Use the book ID assigned by the backend
      const backendBook = response.data.data.book;
      
      // Also save to local storage with the backend-assigned ID
      const books = this.getLocalBooks();
      const bookExists = books.find(b => b.id === backendBook.id);
      if (!bookExists) {
        books.push({
          ...backendBook,
          addedAt: new Date().toISOString()
        });
        localStorage.setItem(FREE_TRIAL_BOOKS_KEY, JSON.stringify(books));
      }
      
      return response.data;
    } catch (error) {
      console.error('Error adding book:', error);
      throw error;
    }
  }

  /**
   * Get books from local storage
   */
  getLocalBooks() {
    try {
      const books = localStorage.getItem(FREE_TRIAL_BOOKS_KEY);
      return books ? JSON.parse(books) : [];
    } catch (error) {
      console.error('Error getting local books:', error);
      return [];
    }
  }

  /**
   * Delete a book from the free trial library
   */
  async deleteBook(bookId) {
    try {
      const response = await axios.delete(`/api/free-trial/books/${bookId}`);
      
      // Also remove from local storage
      const books = this.getLocalBooks();
      const updatedBooks = books.filter(book => book.id !== bookId);
      localStorage.setItem(FREE_TRIAL_BOOKS_KEY, JSON.stringify(updatedBooks));
      
      // Remove associated chat history
      this.removeChatHistory(bookId);
      
      return response.data;
    } catch (error) {
      console.error('Error deleting book:', error);
      throw error;
    }
  }

  /**
   * Remove chat history for a specific book
   */
  removeChatHistory(bookId) {
    try {
      const chats = JSON.parse(localStorage.getItem(FREE_TRIAL_CHAT_KEY) || '{}');
      
      // Remove all chat histories that start with the book ID
      const keysToRemove = Object.keys(chats).filter(key => key.startsWith(`${bookId}_`));
      keysToRemove.forEach(key => delete chats[key]);
      
      localStorage.setItem(FREE_TRIAL_CHAT_KEY, JSON.stringify(chats));
    } catch (error) {
      console.error('Error removing chat history:', error);
    }
  }

  /**
   * Send a chat message as a free trial user
   */
  async sendChatMessage(message, bookId, characterId, bookContext = null) {
    // Defensive: always coerce characterId to string (if not undefined/null)
    if (characterId !== undefined && characterId !== null) {
      characterId = String(characterId);
    }
    // Validation: Ensure all required fields are present and valid
    if (!message || typeof message !== 'string' || message.trim() === '') {
      console.error('sendChatMessage error: message is required and must be a non-empty string.', { message });
      throw new Error('Message is required and must be a non-empty string.');
    }
    if (!bookId || typeof bookId !== 'string' || bookId.trim() === '') {
      console.error('sendChatMessage error: bookId is required and must be a non-empty string.', { bookId });
      throw new Error('Book ID is required and must be a non-empty string.');
    }
    if (!characterId || typeof characterId !== 'string' || characterId.trim() === '') {
      console.error('sendChatMessage error: characterId is required and must be a non-empty string (was: %o)', characterId);
      throw new Error('Character ID is required and must be a non-empty string.');
    }
    try {
      console.log('FreeTrialService - sending message with params:', {
        message,
        book_id: bookId,
        character_id: characterId,
        book_context: bookContext
      });
      
      const response = await axios.post('/api/free-trial/chat/message', {
        message,
        book_id: bookId,
        character_id: characterId,
        book_context: bookContext
      });
      
      console.log('FreeTrialService - received response:', response);
      
      // Handle different response structures
      const responseData = response.data;
      const aiText = responseData?.data?.text || responseData?.text || null;
      
      console.log('FreeTrialService - extracted AI text:', aiText);
      
      if (aiText) {
        // Save message to local storage  
        this.saveMessageToLocal(bookId, characterId, message, aiText);
      } else {
        console.error('No AI response text found in response:', responseData);
      }
      
      return responseData;
    } catch (error) {
      console.error('Error sending chat message:', error);
      console.error('Error details:', error.response?.data);
      throw error;
    }
  }

  /**
   * Save chat messages to local storage
   */
  saveMessageToLocal(bookId, characterId, userMessage, aiResponse) {
    try {
      const chats = JSON.parse(localStorage.getItem(FREE_TRIAL_CHAT_KEY) || '{}');
      const chatKey = `${bookId}_${characterId}`;
      
      if (!chats[chatKey]) {
        chats[chatKey] = [];
      }
      
      // Add user message
      chats[chatKey].push({
        role: 'user',
        content: userMessage,
        timestamp: new Date().toISOString()
      });
      
      // Add AI response
      chats[chatKey].push({
        role: 'assistant',
        content: aiResponse,
        timestamp: new Date().toISOString()
      });
      
      // Keep only last 20 messages per conversation
      if (chats[chatKey].length > 20) {
        chats[chatKey] = chats[chatKey].slice(-20);
      }
      
      localStorage.setItem(FREE_TRIAL_CHAT_KEY, JSON.stringify(chats));
    } catch (error) {
      console.error('Error saving message to local storage:', error);
    }
  }

  /**
   * Get chat history from local storage
   */
  getLocalChatHistory(bookId, characterId) {
    try {
      const chats = JSON.parse(localStorage.getItem(FREE_TRIAL_CHAT_KEY) || '{}');
      const chatKey = `${bookId}_${characterId}`;
      return chats[chatKey] || [];
    } catch (error) {
      console.error('Error getting chat history:', error);
      return [];
    }
  }

  /**
   * Check if user has seen the welcome message
   */
  hasSeenWelcome() {
    try {
      const data = JSON.parse(localStorage.getItem(FREE_TRIAL_KEY) || '{}');
      return data.hasSeenWelcome || false;
    } catch (error) {
      return false;
    }
  }

  /**
   * Mark welcome message as seen
   */
  markWelcomeSeen() {
    try {
      const data = JSON.parse(localStorage.getItem(FREE_TRIAL_KEY) || '{}');
      data.hasSeenWelcome = true;
      localStorage.setItem(FREE_TRIAL_KEY, JSON.stringify(data));
    } catch (error) {
      console.error('Error marking welcome as seen:', error);
    }
  }

  /**
   * Migrate free trial data to authenticated user account
   */
  async migrateToAccount(userId) {
    try {
      const response = await axios.post('/api/free-trial/migrate-to-account', {
        user_id: userId
      });
      
      // Clear local storage after successful migration
      if (response.data.success) {
        this.clearAllData();
      }
      
      return response.data;
    } catch (error) {
      console.error('Error migrating data:', error);
      throw error;
    }
  }

  /**
   * Clear all free trial data from local storage
   */
  clearAllData() {
    localStorage.removeItem(FREE_TRIAL_KEY);
    localStorage.removeItem(FREE_TRIAL_BOOKS_KEY);
    localStorage.removeItem(FREE_TRIAL_CHAT_KEY);
  }

  /**
   * Get book suggestions for free trial users
   */
  async getSuggestions() {
    try {
      const response = await axios.get('/api/free-trial/suggestions');
      return response.data.data.suggestions;
    } catch (error) {
      console.error('Error fetching free trial suggestions:', error);
      // Fallback to hardcoded suggestions if backend fails
      return this.getFallbackSuggestions();
    }
  }

  /**
   * Get a single suggestion for replacement
   */
  async getNewSuggestion() {
    try {
      const response = await axios.get('/api/free-trial/suggestions/single');
      return response.data.data.suggestion;
    } catch (error) {
      console.error('Error fetching new free trial suggestion:', error);
      // Fallback to random suggestion
      const fallback = this.getFallbackSuggestions();
      return fallback[Math.floor(Math.random() * fallback.length)];
    }
  }

  /**
   * Fallback suggestions when backend is unavailable
   */
  getFallbackSuggestions() {
    return [
      {
        id: 'fallback-1',
        title: 'The Midnight Library',
        author: 'Matt Haig',
        description: 'A novel about a library between life and death where each book represents a different path your life could have taken.'
      },
      {
        id: 'fallback-2',
        title: 'Dune',
        author: 'Frank Herbert',
        description: 'Set on the desert planet Arrakis, Dune tells the story of Paul Atreides, whose family accepts stewardship of the planet that produces the spice.'
      },
      {
        id: 'fallback-3',
        title: 'The Seven Husbands of Evelyn Hugo',
        author: 'Taylor Jenkins Reid',
        description: 'A reclusive Hollywood icon finally decides to give her life story to an unknown journalist, revealing stunning secrets.'
      },
      {
        id: 'fallback-4',
        title: 'Educated',
        author: 'Tara Westover',
        description: 'A memoir about a woman who grows up in a survivalist family in Idaho and eventually earns a PhD from Cambridge University.'
      },
      {
        id: 'fallback-5',
        title: 'Atomic Habits',
        author: 'James Clear',
        description: 'A comprehensive guide to building good habits and breaking bad ones, based on the latest research in behavioral psychology.'
      }
    ];
  }

  /**
   * Check if data should be cleared (after 7 days of inactivity)
   */
  checkAndClearOldData() {
    try {
      const data = JSON.parse(localStorage.getItem(FREE_TRIAL_KEY) || '{}');
      if (data.lastAccess) {
        const lastAccess = new Date(data.lastAccess);
        const daysSinceAccess = (new Date() - lastAccess) / (1000 * 60 * 60 * 24);
        
        if (daysSinceAccess > 7) {
          this.clearAllData();
          this.initializeStorage();
        }
      }
      
      // Update last access
      data.lastAccess = new Date().toISOString();
      localStorage.setItem(FREE_TRIAL_KEY, JSON.stringify(data));
    } catch (error) {
      console.error('Error checking old data:', error);
    }
  }
}

// Export singleton instance
export default new FreeTrialService();